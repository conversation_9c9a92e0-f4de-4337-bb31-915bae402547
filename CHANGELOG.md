# 版本更新记录

## v1.0.0 (2023-08-31)

### 新特性

- 完成基础框架搭建，包括前端和后端
- 实现用户认证与授权
- 实现RBAC权限管理
- 实现基础的用户、角色、菜单管理
- 实现部门和岗位管理
- 实现字典管理
- 实现参数配置
- 实现日志管理
- 实现定时任务
- 实现系统监控

### 优化

- 优化页面加载速度
- 优化菜单渲染逻辑
- 优化权限验证流程

### 修复

- 修复菜单权限问题
- 修复部分UI显示问题
- 修复路由跳转异常

## v0.9.0 (2023-07-15)

### 新特性

- 初始化项目结构
- 集成 React18、Redux Toolkit 和 Ant Design
- 集成 NestJS 和 TypeORM
- 搭建基础路由系统
- 实现基础UI组件

### 优化

- 优化项目结构
- 优化开发体验

### 修复

- 修复初始化问题
