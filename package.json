{"name": "ice-frog", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "pnpm -r run start:dev", "docker:mysql": "docker run -p 6666:3306 --name ruoyi-react -e MYSQL_ROOT_PASSWORD=root -d mysql:8.4 --character-set-server=utf8mb4 --collation-server=utf8mb4_general_ci", "db": "rm -rf packages/server/src/entities & npx typeorm-model-generator -h localhost -d ry-vue -p 3306 -u root -x root -e mysql -o packages/server/src/entities --noConfig true --ce pascal --cp camel", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.2.5", "typeorm-model-generator": "^0.4.6"}}