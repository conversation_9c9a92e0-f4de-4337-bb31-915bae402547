{"name": "@ice-frog/admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start:dev": "vite", "build:pro": "vite build", "build": "tsc -b && vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,scss,json,md}\"", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "5.x", "@reduxjs/toolkit": "^2.7.0", "antd": "^5.25.4", "axios": "^1.9.0", "history": "^5.3.0", "lodash-es": "^4.17.21", "normalize.css": "^8.0.1", "react": "^18.3.1", "react-dom": "18.3.1", "react-redux": "^9.2.0", "react-router-dom": "6.30.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@redux-devtools/extension": "^3.3.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "sass-embedded": "^1.87.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vite-plugin-svg-icons": "^2.0.1"}}