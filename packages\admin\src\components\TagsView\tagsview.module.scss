.tags-view-container {
  position: relative;
  top: 55px;
  background: #fff;
  padding: 5px;
  display: flex;
  margin-left: 24px;
  margin-right: 28px;
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: thin;

  /* 隐藏滚动条但保留滚动功能 */
  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  .tags-view-item {
    padding: 5px 10px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    flex-shrink: 0;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      background: #f0f0f0;
    }
    &.active {
      background: #1890ff;
      color: #fff;
      border-color: #1890ff;
    }
    .closeIcon {
      margin-left: 6px;
      font-size: 12px;
      color: #999;
      cursor: pointer;
      transition: color 0.2s;
      vertical-align: middle;
      &:hover {
        color: #ff4d4f;
      }
    }
  }
}
