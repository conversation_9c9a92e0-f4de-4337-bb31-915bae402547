.base-layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  :global {
    .ant-layout {
      height: 100vh;

      .ant-layout-sider {
        height: 100vh;
        position: fixed;
        left: 0;
        top: 0;
        bottom: 0;
        z-index: 100;
        overflow-y: auto;
      }

      .ant-layout-header {
        position: fixed;
        top: 0;
        right: 0;
        z-index: 99;
        width: calc(100% - var(--sider-width));
        transition: all 0.2s;
        background: #fff;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

        &.collapsed {
          width: calc(100% - 80px);
        }
      }

      .main-layout {
        margin-left: var(--sider-width);
        transition: all 0.2s;
        min-height: 100vh;

        &.collapsed {
          margin-left: 80px;
        }

        .main-content {
          margin-top: 40px;
          padding: 24px;
          overflow-y: auto;

          .ant-layout-content {
            background: #fff;
            padding: 24px;
            border-radius: 4px;
            min-height: calc(100vh - 50px - 48px);
          }
        }
      }
    }

    /* Logo 样式 */
    .logo-container {
      height: 50px;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
      font-size: 18px;
      font-weight: bold;
      overflow: hidden;
      white-space: nowrap;
      transition: all 0.3s;
    }
  }
}
