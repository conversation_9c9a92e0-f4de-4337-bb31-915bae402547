.dashboard-container {
  padding: 24px;

  .banner-section {
    display: flex;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 24px;

    @media (max-width: 991px) {
      flex-direction: column;
    }

    .banner-content {
      flex: 1;
      padding: 32px;

      .description {
        font-size: 14px;
        line-height: 1.8;
        color: #666;
        margin-bottom: 24px;
      }

      .tech-stack {
        margin-top: 24px;

        h4 {
          margin-bottom: 16px;
          color: #333;
        }

        .tech-card {
          height: 100%;
          transition: all 0.3s;
          border: 1px solid #eee;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #1890ff;
          }

          :global(.ant-card-body) {
            padding: 16px;
          }
        }
      }
    }

    .banner-image {
      width: 40%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px;

      @media (max-width: 991px) {
        width: 100%;
        height: 240px;
      }

      img {
        max-width: 100%;
        max-height: 400px;
        object-fit: contain;
      }
    }
  }
}
